import { Button } from "primereact/button";
import React from "react";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";

const EmptyPage = () => {

	const sendMail = () => {
		let data = []
		for (const item of data) {
			APIServices.post(API.SendMail, { to: ['<EMAIL>'], subject: item.subject, body: item.body, cc: ["<EMAIL>"] })
		}
	}

	return (
		<div className="grid">
			<div className="col-12">
				<div className="card">
					<h5> Empty Page </h5> <p> Use this page to start from scratch and place your custom content. </p>
					<Button label='Upload' onClick={() => { sendMail() }} />
				</div>
			</div>
		</div>
	);
};

const comparisonFn = function (prevProps, nextProps) {
	return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(EmptyPage, comparisonFn);
